/* PrismJS Theme - Custom Light Theme */
code[class*="language-"],
pre[class*="language-"] {
  color: #24292e;
  background: none;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

pre[class*="language-"]::-moz-selection,
pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection,
code[class*="language-"] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}

pre[class*="language-"]::selection,
pre[class*="language-"] ::selection,
code[class*="language-"]::selection,
code[class*="language-"] ::selection {
  text-shadow: none;
  background: #b3d4fc;
}

@media print {
  code[class*="language-"],
  pre[class*="language-"] {
    text-shadow: none;
  }
}

/* Code blocks */
pre[class*="language-"] {
  padding: 1rem;
  margin: 0.5rem 0;
  overflow: auto;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #f6f8fa;
}

/* Inline code */
:not(pre) > code[class*="language-"] {
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  white-space: normal;
  background: #f3f4f6;
  color: #e11d48;
  border: 1px solid #e5e7eb;
}

/* Tokens */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6a737d;
  font-style: italic;
}

.token.punctuation {
  color: #24292e;
}

.token.namespace {
  opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #005cc5;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #032f62;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #d73a49;
  background: none;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #d73a49;
}

.token.function,
.token.class-name {
  color: #6f42c1;
}

.token.regex,
.token.important,
.token.variable {
  color: #e36209;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* JSON specific */
.language-json .token.property {
  color: #005cc5;
}

.language-json .token.string {
  color: #032f62;
}

.language-json .token.number {
  color: #005cc5;
}

.language-json .token.boolean {
  color: #005cc5;
}

.language-json .token.null {
  color: #005cc5;
}

/* JavaScript specific */
.language-javascript .token.keyword {
  color: #d73a49;
}

.language-javascript .token.function {
  color: #6f42c1;
}

.language-javascript .token.string {
  color: #032f62;
}

/* Python specific */
.language-python .token.keyword {
  color: #d73a49;
}

.language-python .token.function {
  color: #6f42c1;
}

.language-python .token.string {
  color: #032f62;
}

.language-python .token.comment {
  color: #6a737d;
}

/* Bash specific */
.language-bash .token.function {
  color: #6f42c1;
}

.language-bash .token.string {
  color: #032f62;
}

.language-bash .token.parameter {
  color: #24292e;
}

/* HTTP specific */
.language-http .token.request-line {
  color: #d73a49;
}

.language-http .token.header-name {
  color: #005cc5;
}

.language-http .token.header-value {
  color: #032f62;
}

/* Line highlighting */
pre[class*="language-"] .highlight-line {
  background: rgba(255, 255, 0, 0.1);
  display: block;
  margin: 0 -1rem;
  padding: 0 1rem;
}

/* Line numbers */
pre[class*="language-"].line-numbers {
  position: relative;
  padding-left: 3.8rem;
  counter-reset: linenumber;
}

pre[class*="language-"].line-numbers > code {
  position: relative;
  white-space: inherit;
}

.line-numbers .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 0;
  font-size: 100%;
  left: -3.8rem;
  width: 3rem;
  letter-spacing: -1px;
  border-right: 1px solid #e1e4e8;
  user-select: none;
}

.line-numbers-rows > span {
  display: block;
  counter-increment: linenumber;
}

.line-numbers-rows > span:before {
  content: counter(linenumber);
  color: #6a737d;
  display: block;
  padding-right: 0.8rem;
  text-align: right;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  code[class*="language-"],
  pre[class*="language-"] {
    color: #f6f8fa;
  }

  pre[class*="language-"] {
    background: #161b22;
    border-color: #30363d;
  }

  :not(pre) > code[class*="language-"],
  pre[class*="language-"] {
    background: #161b22;
  }

  :not(pre) > code[class*="language-"] {
    background: #21262d;
    color: #f85149;
    border-color: #30363d;
  }

  .token.comment,
  .token.prolog,
  .token.doctype,
  .token.cdata {
    color: #8b949e;
  }

  .token.punctuation {
    color: #f6f8fa;
  }

  .token.property,
  .token.tag,
  .token.boolean,
  .token.number,
  .token.constant,
  .token.symbol,
  .token.deleted {
    color: #79c0ff;
  }

  .token.selector,
  .token.attr-name,
  .token.string,
  .token.char,
  .token.builtin,
  .token.inserted {
    color: #a5d6ff;
  }

  .token.operator,
  .token.entity,
  .token.url,
  .language-css .token.string,
  .style .token.string {
    color: #ff7b72;
  }

  .token.atrule,
  .token.attr-value,
  .token.keyword {
    color: #ff7b72;
  }

  .token.function,
  .token.class-name {
    color: #d2a8ff;
  }

  .token.regex,
  .token.important,
  .token.variable {
    color: #ffa657;
  }

  .line-numbers .line-numbers-rows {
    border-right-color: #30363d;
  }

  .line-numbers-rows > span:before {
    color: #8b949e;
  }
}
